<?php
/**
 * Demir Page Builder Post Types
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Post Types Class
 */
class Demir_Page_Builder_Post_Types {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', array( $this, 'register_post_types' ) );
        add_action( 'init', array( $this, 'add_post_type_support' ) );
    }
    
    /**
     * Register post types
     */
    public function register_post_types() {
        // Register template library post type
        $this->register_template_library();
    }
    
    /**
     * Register template library post type
     */
    private function register_template_library() {
        $labels = array(
            'name'               => __( 'Demir Templates', 'demir' ),
            'singular_name'      => __( 'Template', 'demir' ),
            'menu_name'          => __( 'Demir Templates', 'demir' ),
            'name_admin_bar'     => __( 'Template', 'demir' ),
            'add_new'            => __( 'Add New', 'demir' ),
            'add_new_item'       => __( 'Add New Template', 'demir' ),
            'new_item'           => __( 'New Template', 'demir' ),
            'edit_item'          => __( 'Edit Template', 'demir' ),
            'view_item'          => __( 'View Template', 'demir' ),
            'all_items'          => __( 'All Templates', 'demir' ),
            'search_items'       => __( 'Search Templates', 'demir' ),
            'parent_item_colon'  => __( 'Parent Templates:', 'demir' ),
            'not_found'          => __( 'No templates found.', 'demir' ),
            'not_found_in_trash' => __( 'No templates found in Trash.', 'demir' ),
        );
        
        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'themes.php',
            'query_var'          => true,
            'rewrite'            => false,
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'supports'           => array( 'title', 'editor', 'thumbnail', 'custom-fields' ),
            'show_in_rest'       => true,
        );
        
        register_post_type( 'demir_template', $args );
    }
    
    /**
     * Add post type support
     */
    public function add_post_type_support() {
        // Get supported post types from options or use defaults
        $supported_post_types = get_option( 'demir_page_builder_post_types', array( 'page', 'post' ) );
        
        foreach ( $supported_post_types as $post_type ) {
            add_post_type_support( $post_type, 'demir-page-builder' );
        }
        
        // Add support for template post type
        add_post_type_support( 'demir_template', 'demir-page-builder' );
    }
    
    /**
     * Get supported post types
     */
    public static function get_supported_post_types() {
        return get_post_types_by_support( 'demir-page-builder' );
    }
    
    /**
     * Check if post type is supported
     */
    public static function is_post_type_supported( $post_type ) {
        return post_type_supports( $post_type, 'demir-page-builder' );
    }
}
