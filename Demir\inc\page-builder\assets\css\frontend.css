/**
 * De<PERSON> Page Builder Frontend Styles
 * 
 * @package demir
 * @since 1.0.0
 */

/* Reset and Base Styles */
.demir-page-builder * {
    box-sizing: border-box;
}

.demir-page-builder {
    line-height: 1.6;
    color: #333;
}

/* Section Styles */
.demir-section {
    position: relative;
    width: 100%;
    padding: 60px 0;
}

.demir-section.demir-section-boxed .demir-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.demir-section.demir-section-full-width {
    width: 100%;
}

.demir-section.demir-section-full-width .demir-container {
    max-width: 100%;
    padding: 0;
}

/* Container Styles */
.demir-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Row and Column Styles */
.demir-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.demir-column {
    padding: 0 15px;
    flex: 1;
    min-height: 1px;
}

/* Column <PERSON>idths */
.demir-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.demir-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.demir-col-3 { flex: 0 0 25%; max-width: 25%; }
.demir-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.demir-col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.demir-col-6 { flex: 0 0 50%; max-width: 50%; }
.demir-col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.demir-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.demir-col-9 { flex: 0 0 75%; max-width: 75%; }
.demir-col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.demir-col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.demir-col-12 { flex: 0 0 100%; max-width: 100%; }

/* Widget Base Styles */
.demir-widget {
    margin-bottom: 20px;
    position: relative;
}

.demir-widget:last-child {
    margin-bottom: 0;
}

/* Heading Widget */
.demir-widget-heading h1,
.demir-widget-heading h2,
.demir-widget-heading h3,
.demir-widget-heading h4,
.demir-widget-heading h5,
.demir-widget-heading h6 {
    margin: 0 0 15px 0;
    line-height: 1.2;
}

.demir-widget-heading.demir-align-left { text-align: left; }
.demir-widget-heading.demir-align-center { text-align: center; }
.demir-widget-heading.demir-align-right { text-align: right; }

/* Text Widget */
.demir-widget-text {
    line-height: 1.6;
}

.demir-widget-text p {
    margin: 0 0 15px 0;
}

.demir-widget-text p:last-child {
    margin-bottom: 0;
}

/* Button Widget */
.demir-widget-button {
    display: inline-block;
}

.demir-widget-button.demir-align-left { text-align: left; }
.demir-widget-button.demir-align-center { text-align: center; }
.demir-widget-button.demir-align-right { text-align: right; }

.demir-widget-button a {
    display: inline-block;
    padding: 12px 24px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.3s ease;
    border: 2px solid #0073aa;
}

.demir-widget-button a:hover {
    background: #005a87;
    border-color: #005a87;
}

.demir-widget-button.demir-button-outline a {
    background: transparent;
    color: #0073aa;
}

.demir-widget-button.demir-button-outline a:hover {
    background: #0073aa;
    color: white;
}

/* Image Widget */
.demir-widget-image {
    text-align: center;
}

.demir-widget-image.demir-align-left { text-align: left; }
.demir-widget-image.demir-align-center { text-align: center; }
.demir-widget-image.demir-align-right { text-align: right; }

.demir-widget-image img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

/* Spacer Widget */
.demir-widget-spacer {
    height: 20px;
}

/* Divider Widget */
.demir-widget-divider {
    text-align: center;
    margin: 20px 0;
}

.demir-widget-divider hr {
    border: none;
    height: 1px;
    background: #ddd;
    margin: 0;
}

/* Video Widget */
.demir-widget-video {
    position: relative;
    width: 100%;
}

.demir-widget-video iframe,
.demir-widget-video video {
    width: 100%;
    height: auto;
}

/* Responsive Video */
.demir-video-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.demir-video-responsive iframe,
.demir-video-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Background Styles */
.demir-section.demir-bg-color {
    background-color: #f8f9fa;
}

.demir-section.demir-bg-image {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.demir-section.demir-bg-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.demir-section.demir-bg-overlay > .demir-container {
    position: relative;
    z-index: 2;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .demir-section {
        padding: 40px 0;
    }
}

@media (max-width: 768px) {
    .demir-section {
        padding: 30px 0;
    }
    
    .demir-row {
        flex-direction: column;
        margin: 0;
    }
    
    .demir-column {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        padding: 0;
        margin-bottom: 20px;
    }
    
    .demir-column:last-child {
        margin-bottom: 0;
    }
    
    .demir-container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .demir-section {
        padding: 20px 0;
    }
    
    .demir-container {
        padding: 0 15px;
    }
    
    .demir-widget-button a {
        padding: 10px 20px;
        font-size: 14px;
    }
}

/* Animation Classes */
.demir-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.demir-fade-in.demir-animated {
    opacity: 1;
    transform: translateY(0);
}

.demir-slide-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease;
}

.demir-slide-left.demir-animated {
    opacity: 1;
    transform: translateX(0);
}

.demir-slide-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.6s ease;
}

.demir-slide-right.demir-animated {
    opacity: 1;
    transform: translateX(0);
}
