<?php
/**
 * Demir Page Builder Widgets
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Widgets Class
 */
class Demir_Page_Builder_Widgets {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', array( $this, 'register_shortcodes' ) );
        add_filter( 'the_content', array( $this, 'process_builder_content' ), 9 );
    }
    
    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode( 'demir_section', array( $this, 'render_section' ) );
        add_shortcode( 'demir_column', array( $this, 'render_column' ) );
        add_shortcode( 'demir_heading', array( $this, 'render_heading' ) );
        add_shortcode( 'demir_text', array( $this, 'render_text' ) );
        add_shortcode( 'demir_button', array( $this, 'render_button' ) );
        add_shortcode( 'demir_image', array( $this, 'render_image' ) );
        add_shortcode( 'demir_video', array( $this, 'render_video' ) );
        add_shortcode( 'demir_spacer', array( $this, 'render_spacer' ) );
        add_shortcode( 'demir_divider', array( $this, 'render_divider' ) );
    }
    
    /**
     * Process builder content
     */
    public function process_builder_content( $content ) {
        if ( ! is_singular() ) {
            return $content;
        }
        
        $post_id = get_the_ID();
        $is_builder_enabled = get_post_meta( $post_id, '_demir_page_builder_enabled', true );
        
        if ( 'yes' !== $is_builder_enabled ) {
            return $content;
        }
        
        $builder_data = get_post_meta( $post_id, '_demir_page_builder_data', true );
        
        if ( empty( $builder_data ) ) {
            return $content;
        }
        
        // Parse JSON data
        $data = json_decode( $builder_data, true );
        
        if ( ! $data || ! isset( $data['sections'] ) ) {
            return $content;
        }
        
        return $this->render_builder_content( $data );
    }
    
    /**
     * Render builder content
     */
    public function render_builder_content( $data ) {
        $output = '';
        
        foreach ( $data['sections'] as $section ) {
            $output .= $this->render_section_from_data( $section );
        }
        
        return $output;
    }
    
    /**
     * Render section from data
     */
    private function render_section_from_data( $section_data ) {
        $columns_content = '';
        
        if ( isset( $section_data['columns'] ) ) {
            foreach ( $section_data['columns'] as $column ) {
                $columns_content .= $this->render_column_from_data( $column );
            }
        }
        
        $atts = array(
            'id' => isset( $section_data['id'] ) ? $section_data['id'] : '',
        );
        
        return $this->render_section( $atts, $columns_content );
    }
    
    /**
     * Render column from data
     */
    private function render_column_from_data( $column_data ) {
        $widgets_content = '';
        
        if ( isset( $column_data['widgets'] ) ) {
            foreach ( $column_data['widgets'] as $widget ) {
                $widgets_content .= $this->render_widget_from_data( $widget );
            }
        }
        
        $atts = array(
            'width' => isset( $column_data['width'] ) ? $column_data['width'] : '12',
        );
        
        return $this->render_column( $atts, $widgets_content );
    }
    
    /**
     * Render widget from data
     */
    private function render_widget_from_data( $widget_data ) {
        $type = isset( $widget_data['type'] ) ? $widget_data['type'] : 'text';
        $settings = isset( $widget_data['settings'] ) ? $widget_data['settings'] : array();
        
        switch ( $type ) {
            case 'heading':
                return $this->render_heading( $settings );
            case 'text':
                return $this->render_text( $settings );
            case 'button':
                return $this->render_button( $settings );
            case 'image':
                return $this->render_image( $settings );
            case 'video':
                return $this->render_video( $settings );
            case 'spacer':
                return $this->render_spacer( $settings );
            case 'divider':
                return $this->render_divider( $settings );
            default:
                return '';
        }
    }
    
    /**
     * Render section
     */
    public function render_section( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'id' => '',
            'class' => '',
            'background_color' => '',
            'background_image' => '',
            'padding_top' => '',
            'padding_bottom' => '',
        ), $atts );
        
        $classes = array( 'demir-section' );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $styles = array();
        
        if ( ! empty( $atts['background_color'] ) ) {
            $styles[] = 'background-color: ' . esc_attr( $atts['background_color'] );
        }
        
        if ( ! empty( $atts['background_image'] ) ) {
            $styles[] = 'background-image: url(' . esc_url( $atts['background_image'] ) . ')';
        }
        
        if ( ! empty( $atts['padding_top'] ) ) {
            $styles[] = 'padding-top: ' . esc_attr( $atts['padding_top'] ) . 'px';
        }
        
        if ( ! empty( $atts['padding_bottom'] ) ) {
            $styles[] = 'padding-bottom: ' . esc_attr( $atts['padding_bottom'] ) . 'px';
        }
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '"';
        
        if ( ! empty( $atts['id'] ) ) {
            $output .= ' id="' . esc_attr( $atts['id'] ) . '"';
        }
        
        if ( ! empty( $styles ) ) {
            $output .= ' style="' . esc_attr( implode( '; ', $styles ) ) . '"';
        }
        
        $output .= '>';
        $output .= '<div class="demir-container">';
        $output .= '<div class="demir-row">';
        $output .= do_shortcode( $content );
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render column
     */
    public function render_column( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'width' => '12',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-column', 'demir-col-' . esc_attr( $atts['width'] ) );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">';
        $output .= do_shortcode( $content );
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render heading
     */
    public function render_heading( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'text' => 'Your Heading Text',
            'tag' => 'h2',
            'align' => 'left',
            'color' => '',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-widget', 'demir-widget-heading', 'demir-align-' . esc_attr( $atts['align'] ) );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $styles = array();
        
        if ( ! empty( $atts['color'] ) ) {
            $styles[] = 'color: ' . esc_attr( $atts['color'] );
        }
        
        $tag = in_array( $atts['tag'], array( 'h1', 'h2', 'h3', 'h4', 'h5', 'h6' ) ) ? $atts['tag'] : 'h2';
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">';
        $output .= '<' . $tag;
        
        if ( ! empty( $styles ) ) {
            $output .= ' style="' . esc_attr( implode( '; ', $styles ) ) . '"';
        }
        
        $output .= '>' . esc_html( $atts['text'] ) . '</' . $tag . '>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render text
     */
    public function render_text( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'content' => 'Your text content goes here...',
            'align' => 'left',
            'color' => '',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-widget', 'demir-widget-text', 'demir-align-' . esc_attr( $atts['align'] ) );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $styles = array();
        
        if ( ! empty( $atts['color'] ) ) {
            $styles[] = 'color: ' . esc_attr( $atts['color'] );
        }
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '"';
        
        if ( ! empty( $styles ) ) {
            $output .= ' style="' . esc_attr( implode( '; ', $styles ) ) . '"';
        }
        
        $output .= '>';
        $output .= wpautop( wp_kses_post( $atts['content'] ) );
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render button
     */
    public function render_button( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'text' => 'Click Here',
            'link' => '#',
            'target' => '_self',
            'align' => 'left',
            'style' => 'solid',
            'color' => '',
            'background' => '',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-widget', 'demir-widget-button', 'demir-align-' . esc_attr( $atts['align'] ) );
        
        if ( $atts['style'] === 'outline' ) {
            $classes[] = 'demir-button-outline';
        }
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $link_styles = array();
        
        if ( ! empty( $atts['color'] ) ) {
            $link_styles[] = 'color: ' . esc_attr( $atts['color'] );
        }
        
        if ( ! empty( $atts['background'] ) ) {
            $link_styles[] = 'background-color: ' . esc_attr( $atts['background'] );
            $link_styles[] = 'border-color: ' . esc_attr( $atts['background'] );
        }
        
        $target = in_array( $atts['target'], array( '_self', '_blank' ) ) ? $atts['target'] : '_self';
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">';
        $output .= '<a href="' . esc_url( $atts['link'] ) . '" target="' . esc_attr( $target ) . '"';
        
        if ( ! empty( $link_styles ) ) {
            $output .= ' style="' . esc_attr( implode( '; ', $link_styles ) ) . '"';
        }
        
        $output .= '>' . esc_html( $atts['text'] ) . '</a>';
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render image
     */
    public function render_image( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'src' => '',
            'alt' => '',
            'align' => 'center',
            'width' => '',
            'height' => '',
            'link' => '',
            'class' => '',
        ), $atts );
        
        if ( empty( $atts['src'] ) ) {
            return '';
        }
        
        $classes = array( 'demir-widget', 'demir-widget-image', 'demir-align-' . esc_attr( $atts['align'] ) );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $img_styles = array();
        
        if ( ! empty( $atts['width'] ) ) {
            $img_styles[] = 'width: ' . esc_attr( $atts['width'] ) . 'px';
        }
        
        if ( ! empty( $atts['height'] ) ) {
            $img_styles[] = 'height: ' . esc_attr( $atts['height'] ) . 'px';
        }
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">';
        
        if ( ! empty( $atts['link'] ) ) {
            $output .= '<a href="' . esc_url( $atts['link'] ) . '">';
        }
        
        $output .= '<img src="' . esc_url( $atts['src'] ) . '" alt="' . esc_attr( $atts['alt'] ) . '"';
        
        if ( ! empty( $img_styles ) ) {
            $output .= ' style="' . esc_attr( implode( '; ', $img_styles ) ) . '"';
        }
        
        $output .= '>';
        
        if ( ! empty( $atts['link'] ) ) {
            $output .= '</a>';
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render video
     */
    public function render_video( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'url' => '',
            'width' => '',
            'height' => '',
            'autoplay' => 'false',
            'class' => '',
        ), $atts );
        
        if ( empty( $atts['url'] ) ) {
            return '';
        }
        
        $classes = array( 'demir-widget', 'demir-widget-video' );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $output = '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">';
        
        // Check if it's a YouTube or Vimeo URL
        if ( strpos( $atts['url'], 'youtube.com' ) !== false || strpos( $atts['url'], 'youtu.be' ) !== false ) {
            $output .= $this->render_youtube_video( $atts );
        } elseif ( strpos( $atts['url'], 'vimeo.com' ) !== false ) {
            $output .= $this->render_vimeo_video( $atts );
        } else {
            $output .= $this->render_direct_video( $atts );
        }
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render YouTube video
     */
    private function render_youtube_video( $atts ) {
        // Extract video ID from URL
        preg_match( '/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $atts['url'], $matches );
        
        if ( empty( $matches[1] ) ) {
            return '';
        }
        
        $video_id = $matches[1];
        $autoplay = $atts['autoplay'] === 'true' ? '&autoplay=1' : '';
        
        return '<div class="demir-video-responsive">' .
               '<iframe src="https://www.youtube.com/embed/' . esc_attr( $video_id ) . '?rel=0' . $autoplay . '" ' .
               'frameborder="0" allowfullscreen></iframe>' .
               '</div>';
    }
    
    /**
     * Render Vimeo video
     */
    private function render_vimeo_video( $atts ) {
        // Extract video ID from URL
        preg_match( '/vimeo\.com\/(\d+)/', $atts['url'], $matches );
        
        if ( empty( $matches[1] ) ) {
            return '';
        }
        
        $video_id = $matches[1];
        $autoplay = $atts['autoplay'] === 'true' ? '&autoplay=1' : '';
        
        return '<div class="demir-video-responsive">' .
               '<iframe src="https://player.vimeo.com/video/' . esc_attr( $video_id ) . '?title=0&byline=0&portrait=0' . $autoplay . '" ' .
               'frameborder="0" allowfullscreen></iframe>' .
               '</div>';
    }
    
    /**
     * Render direct video
     */
    private function render_direct_video( $atts ) {
        $autoplay = $atts['autoplay'] === 'true' ? 'autoplay' : '';
        
        return '<video controls ' . $autoplay . '>' .
               '<source src="' . esc_url( $atts['url'] ) . '">' .
               'Your browser does not support the video tag.' .
               '</video>';
    }
    
    /**
     * Render spacer
     */
    public function render_spacer( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'height' => '20',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-widget', 'demir-widget-spacer' );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $height = intval( $atts['height'] );
        
        return '<div class="' . esc_attr( implode( ' ', $classes ) ) . '" style="height: ' . $height . 'px;"></div>';
    }
    
    /**
     * Render divider
     */
    public function render_divider( $atts, $content = null ) {
        $atts = shortcode_atts( array(
            'color' => '#ddd',
            'width' => '100%',
            'height' => '1px',
            'class' => '',
        ), $atts );
        
        $classes = array( 'demir-widget', 'demir-widget-divider' );
        
        if ( ! empty( $atts['class'] ) ) {
            $classes[] = $atts['class'];
        }
        
        $styles = array(
            'background-color: ' . esc_attr( $atts['color'] ),
            'width: ' . esc_attr( $atts['width'] ),
            'height: ' . esc_attr( $atts['height'] ),
        );
        
        return '<div class="' . esc_attr( implode( ' ', $classes ) ) . '">' .
               '<hr style="' . esc_attr( implode( '; ', $styles ) ) . '">' .
               '</div>';
    }
}
