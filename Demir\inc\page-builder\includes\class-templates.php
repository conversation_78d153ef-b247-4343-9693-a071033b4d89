<?php
/**
 * Demir Page Builder Templates
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Templates Class
 */
class Demir_Page_Builder_Templates {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_filter( 'template_include', array( $this, 'template_include' ), 11 );
        add_filter( 'page_template', array( $this, 'page_template' ) );
        add_action( 'wp_head', array( $this, 'add_template_styles' ) );
    }
    
    /**
     * Template include
     */
    public function template_include( $template ) {
        if ( is_singular() ) {
            $post_id = get_the_ID();
            $page_template = get_page_template_slug( $post_id );
            $is_builder_enabled = get_post_meta( $post_id, '_demir_page_builder_enabled', true );
            
            // Only override template if page builder is enabled
            if ( 'yes' === $is_builder_enabled ) {
                if ( 'demir-canvas.php' === $page_template ) {
                    $new_template = DEMIR_PAGE_BUILDER_PATH . 'templates/canvas.php';
                    if ( file_exists( $new_template ) ) {
                        return $new_template;
                    }
                } elseif ( 'demir-fullwidth.php' === $page_template ) {
                    $new_template = DEMIR_PAGE_BUILDER_PATH . 'templates/fullwidth.php';
                    if ( file_exists( $new_template ) ) {
                        return $new_template;
                    }
                }
            }
        }
        
        return $template;
    }
    
    /**
     * Page template
     */
    public function page_template( $template ) {
        if ( is_singular() ) {
            $post_id = get_the_ID();
            $page_template = get_page_template_slug( $post_id );
            $is_builder_enabled = get_post_meta( $post_id, '_demir_page_builder_enabled', true );
            
            // Only override template if page builder is enabled
            if ( 'yes' === $is_builder_enabled ) {
                if ( 'demir-canvas.php' === $page_template ) {
                    return DEMIR_PAGE_BUILDER_PATH . 'templates/canvas.php';
                } elseif ( 'demir-fullwidth.php' === $page_template ) {
                    return DEMIR_PAGE_BUILDER_PATH . 'templates/fullwidth.php';
                }
            }
        }
        
        return $template;
    }
    
    /**
     * Add template styles
     */
    public function add_template_styles() {
        if ( ! is_singular() ) {
            return;
        }
        
        $post_id = get_the_ID();
        $page_template = get_page_template_slug( $post_id );
        $is_builder_enabled = get_post_meta( $post_id, '_demir_page_builder_enabled', true );
        
        if ( 'yes' !== $is_builder_enabled ) {
            return;
        }
        
        ?>
        <style id="demir-page-builder-template-styles">
        /* Canvas Template Styles */
        .demir-canvas-template {
            margin: 0;
            padding: 0;
        }
        
        .demir-canvas-template #demir-canvas-content {
            width: 100%;
            min-height: 100vh;
        }
        
        /* Full Width Template Styles */
        .demir-template-fullwidth #demir-fullwidth-content {
            width: 100%;
        }
        
        .demir-fullwidth-container {
            max-width: 100%;
            padding: 0;
        }
        
        /* Page Builder Common Styles */
        .demir-page-builder .demir-section {
            position: relative;
            width: 100%;
        }
        
        .demir-page-builder .demir-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .demir-page-builder .demir-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }
        
        .demir-page-builder .demir-column {
            padding: 0 15px;
            flex: 1;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .demir-page-builder .demir-row {
                flex-direction: column;
            }
            
            .demir-page-builder .demir-column {
                width: 100%;
                margin-bottom: 20px;
            }
        }
        
        /* Widget Styles */
        .demir-widget {
            margin-bottom: 20px;
        }
        
        .demir-widget-heading h1,
        .demir-widget-heading h2,
        .demir-widget-heading h3,
        .demir-widget-heading h4,
        .demir-widget-heading h5,
        .demir-widget-heading h6 {
            margin: 0 0 15px 0;
        }
        
        .demir-widget-text {
            line-height: 1.6;
        }
        
        .demir-widget-button {
            display: inline-block;
        }
        
        .demir-widget-button a {
            display: inline-block;
            padding: 12px 24px;
            background: #0073aa;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            transition: background 0.3s ease;
        }
        
        .demir-widget-button a:hover {
            background: #005a87;
        }
        
        .demir-widget-image img {
            max-width: 100%;
            height: auto;
        }
        
        .demir-widget-spacer {
            height: 20px;
        }
        </style>
        <?php
    }
    
    /**
     * Get available templates
     */
    public static function get_available_templates() {
        return array(
            'demir-canvas.php' => __( 'Demir Canvas', 'demir' ),
            'demir-fullwidth.php' => __( 'Demir Full Width', 'demir' ),
        );
    }
    
    /**
     * Check if template is page builder template
     */
    public static function is_page_builder_template( $template ) {
        $page_builder_templates = array_keys( self::get_available_templates() );
        return in_array( $template, $page_builder_templates );
    }
}
