<?php
/**
 * Demir Page Builder Canvas Template
 * 
 * Tam sayfa template - header ve footer olmadan
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <?php if ( ! current_theme_supports( 'title-tag' ) ) : ?>
        <title><?php echo wp_get_document_title(); ?></title>
    <?php endif; ?>
    <?php wp_head(); ?>
</head>
<body <?php body_class( 'demir-canvas-template' ); ?>>
    <?php wp_body_open(); ?>
    
    <?php
    /**
     * Before canvas content hook
     */
    do_action( 'demir_page_builder_canvas_before_content' );
    ?>
    
    <div id="demir-canvas-content">
        <?php
        while ( have_posts() ) :
            the_post();
            
            // Check if page builder is enabled
            $is_builder_enabled = get_post_meta( get_the_ID(), '_demir_page_builder_enabled', true );
            
            if ( 'yes' === $is_builder_enabled ) {
                // Render page builder content
                $builder_data = get_post_meta( get_the_ID(), '_demir_page_builder_data', true );
                
                if ( $builder_data ) {
                    echo do_shortcode( $builder_data );
                } else {
                    // Fallback to regular content
                    the_content();
                }
            } else {
                // Regular content
                the_content();
            }
            
        endwhile;
        ?>
    </div>
    
    <?php
    /**
     * After canvas content hook
     */
    do_action( 'demir_page_builder_canvas_after_content' );
    ?>
    
    <?php wp_footer(); ?>
</body>
</html>
