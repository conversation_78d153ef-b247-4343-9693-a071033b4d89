<?php
/**
 * Demir Page Builder Meta Boxes
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Meta Boxes Class
 */
class Demir_Page_Builder_Meta_Boxes {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'add_meta_boxes', array( $this, 'add_meta_boxes' ) );
        add_action( 'save_post', array( $this, 'save_meta_boxes' ) );
        add_action( 'wp_ajax_demir_save_builder_data', array( $this, 'ajax_save_builder_data' ) );
        add_action( 'wp_ajax_demir_load_builder_data', array( $this, 'ajax_load_builder_data' ) );
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = Demir_Page_Builder_Post_Types::get_supported_post_types();
        
        foreach ( $post_types as $post_type ) {
            add_meta_box(
                'demir-page-builder',
                __( 'Demir Page Builder', 'demir' ),
                array( $this, 'render_meta_box' ),
                $post_type,
                'normal',
                'high'
            );
            
            add_meta_box(
                'demir-page-builder-settings',
                __( 'Page Builder Settings', 'demir' ),
                array( $this, 'render_settings_meta_box' ),
                $post_type,
                'side',
                'default'
            );
        }
    }
    
    /**
     * Render main meta box
     */
    public function render_meta_box( $post ) {
        wp_nonce_field( 'demir_page_builder_meta_box', 'demir_page_builder_nonce' );
        
        $builder_data = get_post_meta( $post->ID, '_demir_page_builder_data', true );
        $is_enabled = get_post_meta( $post->ID, '_demir_page_builder_enabled', true );
        
        ?>
        <div id="demir-page-builder-container">
            <div class="demir-builder-toolbar">
                <button type="button" id="demir-add-section" class="button button-primary">
                    <?php _e( 'Add Section', 'demir' ); ?>
                </button>
                <button type="button" id="demir-add-widget" class="button">
                    <?php _e( 'Add Widget', 'demir' ); ?>
                </button>
                <button type="button" id="demir-preview-mode" class="button">
                    <?php _e( 'Preview', 'demir' ); ?>
                </button>
            </div>
            
            <div id="demir-builder-canvas">
                <?php if ( $builder_data ): ?>
                    <textarea id="demir-builder-data" style="display:none;"><?php echo esc_textarea( $builder_data ); ?></textarea>
                <?php endif; ?>
                
                <div id="demir-builder-sections">
                    <!-- Sections will be loaded here via JavaScript -->
                </div>
                
                <div id="demir-add-section-placeholder" class="demir-add-section-placeholder">
                    <button type="button" class="demir-add-section-btn">
                        <span class="dashicons dashicons-plus"></span>
                        <?php _e( 'Add your first section', 'demir' ); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <style>
        #demir-page-builder-container {
            margin: 20px 0;
        }
        
        .demir-builder-toolbar {
            margin-bottom: 20px;
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #ddd;
        }
        
        .demir-builder-toolbar .button {
            margin-right: 10px;
        }
        
        #demir-builder-canvas {
            min-height: 400px;
            border: 1px solid #ddd;
            background: #fff;
            position: relative;
        }
        
        .demir-add-section-placeholder {
            text-align: center;
            padding: 60px 20px;
        }
        
        .demir-add-section-btn {
            background: #0073aa;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .demir-add-section-btn:hover {
            background: #005a87;
        }
        
        .demir-add-section-btn .dashicons {
            margin-right: 5px;
        }
        </style>
        <?php
    }
    
    /**
     * Render settings meta box
     */
    public function render_settings_meta_box( $post ) {
        $is_enabled = get_post_meta( $post->ID, '_demir_page_builder_enabled', true );
        $page_template = get_post_meta( $post->ID, '_wp_page_template', true );
        
        ?>
        <p>
            <label>
                <input type="checkbox" name="demir_page_builder_enabled" value="yes" <?php checked( $is_enabled, 'yes' ); ?>>
                <?php _e( 'Enable Page Builder', 'demir' ); ?>
            </label>
        </p>
        
        <p>
            <label for="demir_page_template"><?php _e( 'Page Template:', 'demir' ); ?></label>
            <select name="demir_page_template" id="demir_page_template" style="width: 100%;">
                <option value="default"><?php _e( 'Default Template', 'demir' ); ?></option>
                <option value="demir-canvas.php" <?php selected( $page_template, 'demir-canvas.php' ); ?>>
                    <?php _e( 'Canvas (No Header/Footer)', 'demir' ); ?>
                </option>
                <option value="demir-fullwidth.php" <?php selected( $page_template, 'demir-fullwidth.php' ); ?>>
                    <?php _e( 'Full Width', 'demir' ); ?>
                </option>
            </select>
        </p>
        
        <p class="description">
            <?php _e( 'Choose how this page should be displayed.', 'demir' ); ?>
        </p>
        <?php
    }
    
    /**
     * Save meta boxes
     */
    public function save_meta_boxes( $post_id ) {
        // Check if our nonce is set
        if ( ! isset( $_POST['demir_page_builder_nonce'] ) ) {
            return;
        }
        
        // Verify that the nonce is valid
        if ( ! wp_verify_nonce( $_POST['demir_page_builder_nonce'], 'demir_page_builder_meta_box' ) ) {
            return;
        }
        
        // If this is an autosave, our form has not been submitted, so we don't want to do anything
        if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
            return;
        }
        
        // Check the user's permissions
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }
        
        // Save page builder enabled status
        $enabled = isset( $_POST['demir_page_builder_enabled'] ) ? 'yes' : 'no';
        update_post_meta( $post_id, '_demir_page_builder_enabled', $enabled );
        
        // Save page template
        if ( isset( $_POST['demir_page_template'] ) && $_POST['demir_page_template'] !== 'default' ) {
            update_post_meta( $post_id, '_wp_page_template', sanitize_text_field( $_POST['demir_page_template'] ) );
        } else {
            delete_post_meta( $post_id, '_wp_page_template' );
        }
    }
    
    /**
     * AJAX save builder data
     */
    public function ajax_save_builder_data() {
        check_ajax_referer( 'demir_page_builder_nonce', 'nonce' );
        
        $post_id = intval( $_POST['post_id'] );
        $builder_data = wp_unslash( $_POST['builder_data'] );
        
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            wp_die( __( 'You do not have permission to edit this post.', 'demir' ) );
        }
        
        update_post_meta( $post_id, '_demir_page_builder_data', $builder_data );
        
        wp_send_json_success( array(
            'message' => __( 'Builder data saved successfully.', 'demir' )
        ) );
    }
    
    /**
     * AJAX load builder data
     */
    public function ajax_load_builder_data() {
        check_ajax_referer( 'demir_page_builder_nonce', 'nonce' );
        
        $post_id = intval( $_POST['post_id'] );
        
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            wp_die( __( 'You do not have permission to edit this post.', 'demir' ) );
        }
        
        $builder_data = get_post_meta( $post_id, '_demir_page_builder_data', true );
        
        wp_send_json_success( array(
            'builder_data' => $builder_data ? $builder_data : ''
        ) );
    }
}
