<?php
/**
 * Demir Page Builder Full Width Template
 * 
 * Header ve footer ile tam genislik template
 * 
 * @package demir
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

get_header();

/**
 * Before fullwidth content hook
 */
do_action( 'demir_page_builder_fullwidth_before_content' );
?>

<div id="demir-fullwidth-content" class="demir-fullwidth-container">
    <?php
    while ( have_posts() ) :
        the_post();
        
        // Check if page builder is enabled
        $is_builder_enabled = get_post_meta( get_the_ID(), '_demir_page_builder_enabled', true );
        
        if ( 'yes' === $is_builder_enabled ) {
            // Render page builder content
            $builder_data = get_post_meta( get_the_ID(), '_demir_page_builder_data', true );
            
            if ( $builder_data ) {
                echo do_shortcode( $builder_data );
            } else {
                // Fallback to regular content
                ?>
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <?php the_content(); ?>
                        </div>
                    </div>
                </div>
                <?php
            }
        } else {
            // Regular content with container
            ?>
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                            <header class="entry-header">
                                <?php the_title( '<h1 class="entry-title">', '</h1>' ); ?>
                            </header>
                            
                            <div class="entry-content">
                                <?php the_content(); ?>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
            <?php
        }
        
    endwhile;
    ?>
</div>

<?php
/**
 * After fullwidth content hook
 */
do_action( 'demir_page_builder_fullwidth_after_content' );

get_footer();
?>
