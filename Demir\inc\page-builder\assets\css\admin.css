/**
 * De<PERSON> Page Builder Admin Styles
 * 
 * @package demir
 * @since 1.0.0
 */

/* Meta Box Styles */
#demir-page-builder-container {
    margin: 20px 0;
    background: #fff;
}

.demir-builder-toolbar {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.demir-builder-toolbar .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

#demir-builder-canvas {
    min-height: 400px;
    border: 1px solid #ddd;
    background: #fff;
    position: relative;
    border-radius: 3px;
}

/* Add Section Placeholder */
.demir-add-section-placeholder {
    text-align: center;
    padding: 60px 20px;
    border: 2px dashed #ddd;
    margin: 20px;
    border-radius: 3px;
    background: #fafafa;
}

.demir-add-section-btn {
    background: #0073aa;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease;
}

.demir-add-section-btn:hover {
    background: #005a87;
}

.demir-add-section-btn .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Section Styles */
.demir-builder-section {
    border: 1px solid #e1e1e1;
    margin: 10px;
    background: #fff;
    border-radius: 3px;
    position: relative;
}

.demir-builder-section:hover {
    border-color: #0073aa;
}

.demir-section-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.demir-section-title {
    font-weight: 600;
    color: #333;
}

.demir-section-controls {
    display: flex;
    gap: 5px;
}

.demir-section-controls .button {
    padding: 2px 8px;
    font-size: 12px;
    line-height: 1.4;
    min-height: auto;
}

.demir-section-content {
    padding: 20px;
    min-height: 100px;
}

/* Column Styles */
.demir-builder-columns {
    display: flex;
    gap: 15px;
}

.demir-builder-column {
    flex: 1;
    border: 1px dashed #ddd;
    min-height: 80px;
    padding: 15px;
    background: #fafafa;
    border-radius: 3px;
    position: relative;
}

.demir-builder-column:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.demir-column-placeholder {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

/* Widget Styles */
.demir-builder-widget {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 3px;
    margin-bottom: 10px;
    position: relative;
}

.demir-builder-widget:hover {
    border-color: #0073aa;
}

.demir-widget-header {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.demir-widget-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

.demir-widget-controls {
    display: flex;
    gap: 3px;
}

.demir-widget-controls .button {
    padding: 1px 6px;
    font-size: 11px;
    line-height: 1.2;
    min-height: auto;
}

.demir-widget-content {
    padding: 12px;
}

/* Widget Panel */
.demir-widget-panel {
    position: fixed;
    top: 32px;
    right: -300px;
    width: 300px;
    height: calc(100vh - 32px);
    background: #fff;
    border-left: 1px solid #ddd;
    z-index: 9999;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.demir-widget-panel.demir-panel-open {
    right: 0;
}

.demir-panel-header {
    padding: 15px;
    border-bottom: 1px solid #ddd;
    background: #f8f9fa;
}

.demir-panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.demir-panel-close {
    float: right;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

.demir-panel-content {
    padding: 15px;
}

/* Widget Library */
.demir-widget-library {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.demir-widget-item {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.demir-widget-item:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.demir-widget-item .dashicons {
    font-size: 24px;
    margin-bottom: 5px;
    color: #666;
}

.demir-widget-item-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

/* Form Styles */
.demir-form-group {
    margin-bottom: 15px;
}

.demir-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.demir-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.demir-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.demir-form-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: #fff;
    font-size: 14px;
}

.demir-form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
    min-height: 80px;
    resize: vertical;
}

/* Color Picker */
.demir-color-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.demir-color-preview {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

/* Responsive */
@media (max-width: 1024px) {
    .demir-widget-panel {
        width: 280px;
        right: -280px;
    }
}

@media (max-width: 768px) {
    .demir-builder-columns {
        flex-direction: column;
    }
    
    .demir-widget-panel {
        width: 100%;
        right: -100%;
    }
    
    .demir-builder-toolbar .button {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* Loading States */
.demir-loading {
    position: relative;
    pointer-events: none;
}

.demir-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.demir-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #0073aa;
    border-top-color: transparent;
    border-radius: 50%;
    animation: demir-spin 1s linear infinite;
    z-index: 11;
}

@keyframes demir-spin {
    to {
        transform: rotate(360deg);
    }
}
