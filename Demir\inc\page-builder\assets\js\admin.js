/**
 * Demir Page Builder Admin JavaScript
 * 
 * @package demir
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Main Admin Class
     */
    var DemirPageBuilderAdmin = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.initSortable();
            this.loadBuilderData();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            $(document).on('click', '#demir-add-section, .demir-add-section-btn', this.addSection.bind(this));
            $(document).on('click', '#demir-add-widget', this.openWidgetPanel.bind(this));
            $(document).on('click', '.demir-panel-close', this.closeWidgetPanel.bind(this));
            $(document).on('click', '.demir-widget-item', this.addWidget.bind(this));
            $(document).on('click', '.demir-remove-section', this.removeSection.bind(this));
            $(document).on('click', '.demir-remove-widget', this.removeWidget.bind(this));
            $(document).on('click', '.demir-edit-widget', this.editWidget.bind(this));
            $(document).on('change', '#demir-builder-canvas input, #demir-builder-canvas select, #demir-builder-canvas textarea', this.saveBuilderData.bind(this));
            $(document).on('click', '#demir-preview-mode', this.togglePreviewMode.bind(this));
            
            // Auto-save
            setInterval(this.saveBuilderData.bind(this), 30000); // Auto-save every 30 seconds
        },
        
        /**
         * Add new section
         */
        addSection: function(e) {
            e.preventDefault();
            
            var sectionHtml = this.getSectionTemplate();
            
            if ($('#demir-builder-sections .demir-builder-section').length === 0) {
                $('#demir-add-section-placeholder').hide();
            }
            
            $('#demir-builder-sections').append(sectionHtml);
            this.initSortable();
            this.saveBuilderData();
        },
        
        /**
         * Get section template
         */
        getSectionTemplate: function() {
            var sectionId = 'section_' + Date.now();
            
            return '<div class="demir-builder-section" data-section-id="' + sectionId + '">' +
                '<div class="demir-section-header">' +
                    '<span class="demir-section-title">Section</span>' +
                    '<div class="demir-section-controls">' +
                        '<button type="button" class="button demir-edit-section">Edit</button>' +
                        '<button type="button" class="button demir-remove-section">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-section-content">' +
                    '<div class="demir-builder-columns">' +
                        '<div class="demir-builder-column" data-column-width="12">' +
                            '<div class="demir-column-placeholder">Drop widgets here or click "Add Widget"</div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Remove section
         */
        removeSection: function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to remove this section?')) {
                $(e.target).closest('.demir-builder-section').remove();
                
                if ($('#demir-builder-sections .demir-builder-section').length === 0) {
                    $('#demir-add-section-placeholder').show();
                }
                
                this.saveBuilderData();
            }
        },
        
        /**
         * Open widget panel
         */
        openWidgetPanel: function(e) {
            e.preventDefault();
            
            if ($('.demir-widget-panel').length === 0) {
                this.createWidgetPanel();
            }
            
            $('.demir-widget-panel').addClass('demir-panel-open');
        },
        
        /**
         * Close widget panel
         */
        closeWidgetPanel: function(e) {
            e.preventDefault();
            $('.demir-widget-panel').removeClass('demir-panel-open');
        },
        
        /**
         * Create widget panel
         */
        createWidgetPanel: function() {
            var panelHtml = '<div class="demir-widget-panel">' +
                '<div class="demir-panel-header">' +
                    '<h3 class="demir-panel-title">Add Widget</h3>' +
                    '<button class="demir-panel-close">&times;</button>' +
                '</div>' +
                '<div class="demir-panel-content">' +
                    '<div class="demir-widget-library">' +
                        this.getWidgetLibraryHtml() +
                    '</div>' +
                '</div>' +
            '</div>';
            
            $('body').append(panelHtml);
        },
        
        /**
         * Get widget library HTML
         */
        getWidgetLibraryHtml: function() {
            var widgets = [
                { id: 'heading', title: 'Heading', icon: 'dashicons-heading' },
                { id: 'text', title: 'Text', icon: 'dashicons-text' },
                { id: 'button', title: 'Button', icon: 'dashicons-button' },
                { id: 'image', title: 'Image', icon: 'dashicons-format-image' },
                { id: 'video', title: 'Video', icon: 'dashicons-video-alt3' },
                { id: 'spacer', title: 'Spacer', icon: 'dashicons-minus' },
                { id: 'divider', title: 'Divider', icon: 'dashicons-minus' }
            ];
            
            var html = '';
            
            widgets.forEach(function(widget) {
                html += '<div class="demir-widget-item" data-widget-type="' + widget.id + '">' +
                    '<div class="dashicons ' + widget.icon + '"></div>' +
                    '<div class="demir-widget-item-title">' + widget.title + '</div>' +
                '</div>';
            });
            
            return html;
        },
        
        /**
         * Add widget
         */
        addWidget: function(e) {
            e.preventDefault();
            
            var $item = $(e.currentTarget);
            var widgetType = $item.data('widget-type');
            var widgetHtml = this.getWidgetTemplate(widgetType);
            
            // Find the first available column or create one
            var $column = $('.demir-builder-column').first();
            
            if ($column.length === 0) {
                this.addSection();
                $column = $('.demir-builder-column').first();
            }
            
            $column.find('.demir-column-placeholder').hide();
            $column.append(widgetHtml);
            
            this.closeWidgetPanel();
            this.initSortable();
            this.saveBuilderData();
        },
        
        /**
         * Get widget template
         */
        getWidgetTemplate: function(type) {
            var widgetId = 'widget_' + Date.now();
            var templates = {
                'heading': this.getHeadingWidgetTemplate(widgetId),
                'text': this.getTextWidgetTemplate(widgetId),
                'button': this.getButtonWidgetTemplate(widgetId),
                'image': this.getImageWidgetTemplate(widgetId),
                'video': this.getVideoWidgetTemplate(widgetId),
                'spacer': this.getSpacerWidgetTemplate(widgetId),
                'divider': this.getDividerWidgetTemplate(widgetId)
            };
            
            return templates[type] || templates['text'];
        },
        
        /**
         * Get heading widget template
         */
        getHeadingWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="heading">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Heading</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-edit-widget">Edit</button>' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Text:</label>' +
                        '<input type="text" class="demir-form-control" name="text" value="Your Heading Text" />' +
                    '</div>' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Tag:</label>' +
                        '<select class="demir-form-select" name="tag">' +
                            '<option value="h1">H1</option>' +
                            '<option value="h2" selected>H2</option>' +
                            '<option value="h3">H3</option>' +
                            '<option value="h4">H4</option>' +
                            '<option value="h5">H5</option>' +
                            '<option value="h6">H6</option>' +
                        '</select>' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get text widget template
         */
        getTextWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="text">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Text</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-edit-widget">Edit</button>' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Content:</label>' +
                        '<textarea class="demir-form-textarea" name="content">Your text content goes here...</textarea>' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get button widget template
         */
        getButtonWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="button">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Button</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-edit-widget">Edit</button>' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Text:</label>' +
                        '<input type="text" class="demir-form-control" name="text" value="Click Here" />' +
                    '</div>' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Link:</label>' +
                        '<input type="url" class="demir-form-control" name="link" value="#" />' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get image widget template
         */
        getImageWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="image">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Image</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-edit-widget">Edit</button>' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Image URL:</label>' +
                        '<input type="url" class="demir-form-control" name="src" value="" placeholder="Enter image URL" />' +
                    '</div>' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Alt Text:</label>' +
                        '<input type="text" class="demir-form-control" name="alt" value="" />' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get video widget template
         */
        getVideoWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="video">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Video</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-edit-widget">Edit</button>' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Video URL:</label>' +
                        '<input type="url" class="demir-form-control" name="url" value="" placeholder="YouTube, Vimeo or direct video URL" />' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get spacer widget template
         */
        getSpacerWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="spacer">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Spacer</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<div class="demir-form-group">' +
                        '<label class="demir-form-label">Height (px):</label>' +
                        '<input type="number" class="demir-form-control" name="height" value="20" min="1" />' +
                    '</div>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Get divider widget template
         */
        getDividerWidgetTemplate: function(id) {
            return '<div class="demir-builder-widget" data-widget-id="' + id + '" data-widget-type="divider">' +
                '<div class="demir-widget-header">' +
                    '<span class="demir-widget-title">Divider</span>' +
                    '<div class="demir-widget-controls">' +
                        '<button type="button" class="button demir-remove-widget">Remove</button>' +
                    '</div>' +
                '</div>' +
                '<div class="demir-widget-content">' +
                    '<p>Horizontal divider line</p>' +
                '</div>' +
            '</div>';
        },
        
        /**
         * Remove widget
         */
        removeWidget: function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to remove this widget?')) {
                var $widget = $(e.target).closest('.demir-builder-widget');
                var $column = $widget.closest('.demir-builder-column');
                
                $widget.remove();
                
                if ($column.find('.demir-builder-widget').length === 0) {
                    $column.find('.demir-column-placeholder').show();
                }
                
                this.saveBuilderData();
            }
        },
        
        /**
         * Edit widget
         */
        editWidget: function(e) {
            e.preventDefault();
            
            var $widget = $(e.target).closest('.demir-builder-widget');
            var $content = $widget.find('.demir-widget-content');
            
            $content.toggle();
        },
        
        /**
         * Initialize sortable
         */
        initSortable: function() {
            if (typeof $.fn.sortable !== 'undefined') {
                $('#demir-builder-sections').sortable({
                    handle: '.demir-section-header',
                    placeholder: 'demir-section-placeholder',
                    update: this.saveBuilderData.bind(this)
                });
                
                $('.demir-builder-column').sortable({
                    handle: '.demir-widget-header',
                    placeholder: 'demir-widget-placeholder',
                    connectWith: '.demir-builder-column',
                    update: this.saveBuilderData.bind(this)
                });
            }
        },
        
        /**
         * Save builder data
         */
        saveBuilderData: function() {
            var builderData = this.serializeBuilderData();
            
            $.ajax({
                url: demirPageBuilder.ajaxurl,
                type: 'POST',
                data: {
                    action: 'demir_save_builder_data',
                    nonce: demirPageBuilder.nonce,
                    post_id: demirPageBuilder.post_id,
                    builder_data: JSON.stringify(builderData)
                },
                success: function(response) {
                    if (response.success) {
                        console.log('Builder data saved successfully');
                    }
                }
            });
        },
        
        /**
         * Load builder data
         */
        loadBuilderData: function() {
            var existingData = $('#demir-builder-data').val();
            
            if (existingData) {
                try {
                    var data = JSON.parse(existingData);
                    this.renderBuilderData(data);
                } catch (e) {
                    console.error('Error parsing builder data:', e);
                }
            }
        },
        
        /**
         * Serialize builder data
         */
        serializeBuilderData: function() {
            var data = {
                sections: []
            };
            
            $('#demir-builder-sections .demir-builder-section').each(function() {
                var $section = $(this);
                var sectionData = {
                    id: $section.data('section-id'),
                    columns: []
                };
                
                $section.find('.demir-builder-column').each(function() {
                    var $column = $(this);
                    var columnData = {
                        width: $column.data('column-width'),
                        widgets: []
                    };
                    
                    $column.find('.demir-builder-widget').each(function() {
                        var $widget = $(this);
                        var widgetData = {
                            id: $widget.data('widget-id'),
                            type: $widget.data('widget-type'),
                            settings: {}
                        };
                        
                        $widget.find('input, select, textarea').each(function() {
                            var $field = $(this);
                            var name = $field.attr('name');
                            if (name) {
                                widgetData.settings[name] = $field.val();
                            }
                        });
                        
                        columnData.widgets.push(widgetData);
                    });
                    
                    sectionData.columns.push(columnData);
                });
                
                data.sections.push(sectionData);
            });
            
            return data;
        },
        
        /**
         * Render builder data
         */
        renderBuilderData: function(data) {
            if (!data || !data.sections) {
                return;
            }
            
            var $container = $('#demir-builder-sections');
            $container.empty();
            
            data.sections.forEach(function(section) {
                var sectionHtml = this.getSectionTemplate();
                var $section = $(sectionHtml);
                $section.attr('data-section-id', section.id);
                
                var $columnsContainer = $section.find('.demir-builder-columns');
                $columnsContainer.empty();
                
                section.columns.forEach(function(column) {
                    var $column = $('<div class="demir-builder-column"></div>');
                    $column.attr('data-column-width', column.width);
                    
                    if (column.widgets && column.widgets.length > 0) {
                        column.widgets.forEach(function(widget) {
                            var widgetHtml = this.getWidgetTemplate(widget.type);
                            var $widget = $(widgetHtml);
                            $widget.attr('data-widget-id', widget.id);
                            
                            // Set widget settings
                            if (widget.settings) {
                                Object.keys(widget.settings).forEach(function(key) {
                                    $widget.find('[name="' + key + '"]').val(widget.settings[key]);
                                });
                            }
                            
                            $column.append($widget);
                        }.bind(this));
                    } else {
                        $column.append('<div class="demir-column-placeholder">Drop widgets here or click "Add Widget"</div>');
                    }
                    
                    $columnsContainer.append($column);
                }.bind(this));
                
                $container.append($section);
            }.bind(this));
            
            if (data.sections.length > 0) {
                $('#demir-add-section-placeholder').hide();
            }
            
            this.initSortable();
        },
        
        /**
         * Toggle preview mode
         */
        togglePreviewMode: function(e) {
            e.preventDefault();
            
            var $button = $(e.target);
            var $canvas = $('#demir-builder-canvas');
            
            if ($canvas.hasClass('demir-preview-mode')) {
                $canvas.removeClass('demir-preview-mode');
                $button.text('Preview');
            } else {
                $canvas.addClass('demir-preview-mode');
                $button.text('Edit');
            }
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        if ($('#demir-page-builder-container').length) {
            DemirPageBuilderAdmin.init();
        }
    });
    
})(jQuery);
