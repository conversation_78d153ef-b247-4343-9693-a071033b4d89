/**
 * Demir Page Builder Frontend JavaScript
 * 
 * @package demir
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Main Frontend Class
     */
    var DemirPageBuilderFrontend = {
        
        /**
         * Initialize
         */
        init: function() {
            this.bindEvents();
            this.initAnimations();
            this.initResponsiveVideos();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            $(window).on('load', this.onWindowLoad.bind(this));
            $(window).on('resize', this.onWindowResize.bind(this));
            $(window).on('scroll', this.onWindowScroll.bind(this));
        },
        
        /**
         * Window load event
         */
        onWindowLoad: function() {
            this.triggerAnimations();
        },
        
        /**
         * Window resize event
         */
        onWindowResize: function() {
            this.handleResponsiveElements();
        },
        
        /**
         * Window scroll event
         */
        onWindowScroll: function() {
            this.triggerAnimations();
        },
        
        /**
         * Initialize animations
         */
        initAnimations: function() {
            var $animatedElements = $('.demir-fade-in, .demir-slide-left, .demir-slide-right');
            
            if ($animatedElements.length) {
                this.setupIntersectionObserver($animatedElements);
            }
        },
        
        /**
         * Setup intersection observer for animations
         */
        setupIntersectionObserver: function($elements) {
            if ('IntersectionObserver' in window) {
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            $(entry.target).addClass('demir-animated');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });
                
                $elements.each(function() {
                    observer.observe(this);
                });
            } else {
                // Fallback for older browsers
                this.triggerAnimations();
            }
        },
        
        /**
         * Trigger animations (fallback)
         */
        triggerAnimations: function() {
            var $window = $(window);
            var windowTop = $window.scrollTop();
            var windowBottom = windowTop + $window.height();
            
            $('.demir-fade-in, .demir-slide-left, .demir-slide-right').each(function() {
                var $element = $(this);
                var elementTop = $element.offset().top;
                
                if (elementTop < windowBottom - 50 && !$element.hasClass('demir-animated')) {
                    $element.addClass('demir-animated');
                }
            });
        },
        
        /**
         * Initialize responsive videos
         */
        initResponsiveVideos: function() {
            $('.demir-widget-video iframe, .demir-widget-video video').each(function() {
                var $video = $(this);
                var $container = $video.closest('.demir-widget-video');
                
                if (!$container.hasClass('demir-video-responsive')) {
                    $container.addClass('demir-video-responsive');
                }
            });
        },
        
        /**
         * Handle responsive elements
         */
        handleResponsiveElements: function() {
            this.handleResponsiveColumns();
            this.handleResponsiveSpacing();
        },
        
        /**
         * Handle responsive columns
         */
        handleResponsiveColumns: function() {
            var windowWidth = $(window).width();
            
            $('.demir-row').each(function() {
                var $row = $(this);
                var $columns = $row.find('.demir-column');
                
                if (windowWidth <= 768) {
                    $columns.css({
                        'width': '100%',
                        'max-width': '100%',
                        'flex': '0 0 100%'
                    });
                } else {
                    $columns.removeAttr('style');
                }
            });
        },
        
        /**
         * Handle responsive spacing
         */
        handleResponsiveSpacing: function() {
            var windowWidth = $(window).width();
            
            $('.demir-section').each(function() {
                var $section = $(this);
                var originalPadding = $section.data('original-padding');
                
                if (!originalPadding) {
                    originalPadding = $section.css('padding-top');
                    $section.data('original-padding', originalPadding);
                }
                
                if (windowWidth <= 768) {
                    $section.css('padding', '30px 0');
                } else if (windowWidth <= 1024) {
                    $section.css('padding', '40px 0');
                } else {
                    $section.css('padding', originalPadding + ' 0');
                }
            });
        },
        
        /**
         * Smooth scroll to anchor
         */
        smoothScrollToAnchor: function(target) {
            var $target = $(target);
            
            if ($target.length) {
                $('html, body').animate({
                    scrollTop: $target.offset().top - 80
                }, 800);
            }
        },
        
        /**
         * Initialize parallax effect
         */
        initParallax: function() {
            $('.demir-section[data-parallax="true"]').each(function() {
                var $section = $(this);
                var speed = $section.data('parallax-speed') || 0.5;
                
                $(window).on('scroll', function() {
                    var scrollTop = $(window).scrollTop();
                    var offset = $section.offset().top;
                    var yPos = -(scrollTop - offset) * speed;
                    
                    $section.css('background-position', 'center ' + yPos + 'px');
                });
            });
        },
        
        /**
         * Handle background videos
         */
        initBackgroundVideos: function() {
            $('.demir-section[data-bg-video]').each(function() {
                var $section = $(this);
                var videoUrl = $section.data('bg-video');
                
                if (videoUrl) {
                    var $video = $('<video autoplay muted loop playsinline></video>');
                    $video.attr('src', videoUrl);
                    $video.css({
                        'position': 'absolute',
                        'top': '0',
                        'left': '0',
                        'width': '100%',
                        'height': '100%',
                        'object-fit': 'cover',
                        'z-index': '-1'
                    });
                    
                    $section.css('position', 'relative').prepend($video);
                }
            });
        },
        
        /**
         * Initialize lightbox for images
         */
        initLightbox: function() {
            $('.demir-widget-image img[data-lightbox="true"]').on('click', function(e) {
                e.preventDefault();
                
                var $img = $(this);
                var src = $img.attr('src');
                var alt = $img.attr('alt') || '';
                
                var $lightbox = $('<div class="demir-lightbox"></div>');
                var $overlay = $('<div class="demir-lightbox-overlay"></div>');
                var $content = $('<div class="demir-lightbox-content"></div>');
                var $image = $('<img src="' + src + '" alt="' + alt + '">');
                var $close = $('<button class="demir-lightbox-close">&times;</button>');
                
                $content.append($image, $close);
                $lightbox.append($overlay, $content);
                $('body').append($lightbox);
                
                // Show lightbox
                setTimeout(function() {
                    $lightbox.addClass('demir-lightbox-open');
                }, 10);
                
                // Close events
                $overlay.add($close).on('click', function() {
                    $lightbox.removeClass('demir-lightbox-open');
                    setTimeout(function() {
                        $lightbox.remove();
                    }, 300);
                });
                
                // ESC key
                $(document).on('keyup.lightbox', function(e) {
                    if (e.keyCode === 27) {
                        $close.click();
                        $(document).off('keyup.lightbox');
                    }
                });
            });
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        DemirPageBuilderFrontend.init();
    });
    
    /**
     * Make it globally accessible
     */
    window.DemirPageBuilderFrontend = DemirPageBuilderFrontend;
    
})(jQuery);
